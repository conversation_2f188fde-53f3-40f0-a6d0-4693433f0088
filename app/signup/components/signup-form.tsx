"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { ArrowLeft, ArrowRight, MapPin } from "lucide-react"
import { MonthSelector } from "@/components/month-selector"
import { DestinationAutocomplete } from "@/app/(authenticated)/trips/create/components/destination-autocomplete"
import { createUserWithEmailAndPassword, updateProfile } from "firebase/auth"
import { auth } from "@/lib/firebase"
import { toast } from "@/components/ui/use-toast"
import { doc, setDoc, serverTimestamp } from "firebase/firestore"
import { db } from "@/lib/firebase"
// import { SUBSCRIPTION_LIMITS, AI_USAGE_LIMITS } from "@/lib/domains/user-ai-usage/user-ai-usage.types"

export function SignupForm() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [step, setStep] = useState(1)
  const [selectedMonths, setSelectedMonths] = useState<string[]>([])
  const [selectedTravelPreferences, setSelectedTravelPreferences] = useState<string[]>([])
  const [selectedAvailability, setSelectedAvailability] = useState<string[]>([])
  const [selectedTravelGroups, setSelectedTravelGroups] = useState<string[]>([])
  const [budget, setBudget] = useState("mid-range")
  const [location, setLocation] = useState("")
  const [locationPlaceId, setLocationPlaceId] = useState<string | undefined>()
  const [loading, setLoading] = useState(false)
  const [callbackUrl, setCallbackUrl] = useState<string | null>(null)
  const [invitedEmail, setInvitedEmail] = useState<string | null>(null)
  const totalSteps = 4

  // Extract callback URL and invited email from search params if they exist
  useEffect(() => {
    const callback = searchParams.get("callback")
    if (callback) {
      setCallbackUrl(callback)
    }

    const email = searchParams.get("invited_email")
    if (email) {
      setInvitedEmail(email)
      // Pre-fill the email field
      setFormData((prev) => ({ ...prev, email }))
    }
  }, [searchParams])

  // Form data
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    name: "",
    bio: "",
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target
    setFormData((prev) => ({ ...prev, [id]: value }))
  }

  const togglePreference = (
    preference: string,
    setter: React.Dispatch<React.SetStateAction<string[]>>,
    current: string[]
  ) => {
    if (current.includes(preference)) {
      setter(current.filter((p) => p !== preference))
    } else {
      setter([...current, preference])
    }
  }

  const nextStep = () => {
    if (step < totalSteps) {
      setStep(step + 1)
    }
  }

  const prevStep = () => {
    if (step > 1) {
      setStep(step - 1)
    }
  }

  const handleSignup = async () => {
    if (!formData.email || !formData.password || !formData.name) {
      toast({
        title: "Missing information",
        description: "Please fill in all required fields",
        variant: "destructive",
      })
      return
    }

    setLoading(true)
    try {
      // Create user account with Firebase Authentication
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        formData.email,
        formData.password
      )

      const userId = userCredential.user.uid

      // Update the user profile with the display name
      await updateProfile(userCredential.user, {
        displayName: formData.name,
      })

      // 1. Save user document to Firestore
      const userRef = doc(db, "users", userId)
      await setDoc(userRef, {
        uid: userId,
        email: formData.email,
        displayName: formData.name,
        photoURL: null,
        bio: formData.bio || "",
        location: location || null,
        locationPlaceId: locationPlaceId || null,
        createdAt: serverTimestamp(),
        travelPreferences: selectedTravelPreferences,
        budgetRange: budget,
        availabilityPreferences: selectedAvailability,
        preferredTravelSeasons: selectedMonths,
        travelGroupPreferences: selectedTravelGroups,
      })

      // 2. Initialize user subscription document
      // const subscriptionRef = doc(db, "userSubscriptions", userId)
      // await setDoc(subscriptionRef, {
      //   userId,
      //   subscriptionPlan: "free",
      //   subscriptionStatus: "active",
      //   maxSquads: SUBSCRIPTION_LIMITS.FREE.MAX_SQUADS,
      //   maxTripsPerSquad: SUBSCRIPTION_LIMITS.FREE.MAX_TRIPS_PER_SQUAD,
      //   maxDailyAIRequests: AI_USAGE_LIMITS.FREE.DAILY,
      //   maxWeeklyAIRequests: AI_USAGE_LIMITS.FREE.WEEKLY,
      //   createdAt: serverTimestamp(),
      //   updatedAt: serverTimestamp(),
      // })

      // 3. Initialize user preferences document
      const preferencesRef = doc(db, "userPreferences", userId)
      await setDoc(preferencesRef, {
        userId,
        theme: "system",
        location: location || null,
        locationPlaceId: locationPlaceId || null,
        travelPreferences: selectedTravelPreferences,
        budgetRange:
          budget === "budget-friendly"
            ? [0, 500]
            : budget === "mid-range"
              ? [500, 2000]
              : [2000, 10000],
        availabilityPreferences: selectedAvailability,
        preferredTravelSeasons: selectedMonths,
        travelGroupPreferences: selectedTravelGroups,
        aiEnabled: true,
        proactiveSuggestions: true,
        notificationsEnabled: true,
        emailNotifications: true,
        pushNotifications: true,
        tripUpdatesNotifications: true,
        squadMessagesNotifications: true,
        invitationNotifications: true,
        aiSuggestionsNotifications: true,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      })

      // 4. Initialize user AI usage document
      const aiUsageRef = doc(db, "userAiUsage", userId)
      await setDoc(aiUsageRef, {
        userId,
        aiUsageToday: 0,
        aiUsageThisWeek: 0,
        aiUsageLastReset: serverTimestamp(),
        aiUsageWeekStart: serverTimestamp(),
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      })

      // Show success message
      toast({
        title: "Account created successfully!",
        description: "Please log in with your new credentials.",
      })

      // Redirect to login page with callback if it exists
      if (callbackUrl) {
        router.push(`/login?callback=${encodeURIComponent(callbackUrl)}`)
      } else {
        router.push("/login?message=account_created")
      }
    } catch (error: any) {
      console.error("Signup error:", error)

      // Handle specific Firebase auth errors
      if (error.code === "auth/email-already-in-use") {
        toast({
          title: "Email already in use",
          description:
            "This email address is already registered. Please log in or use a different email.",
          variant: "destructive",
        })
      } else {
        toast({
          title: "Error creating account",
          description: error.message || "Something went wrong. Please try again.",
          variant: "destructive",
        })
      }
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex flex-col">
      <header className="border-b">
        <div className="container mx-auto px-4 py-4">
          <Link href="/" className="flex items-center gap-2">
            <MapPin className="h-5 w-5 text-primary" />
            <span className="font-semibold">Togeda.ai</span>
          </Link>
        </div>
      </header>

      <main className="flex-1 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Create your account</CardTitle>
            <CardDescription>
              Step {step} of {totalSteps}: {getStepDescription(step)}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {step === 1 && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={handleInputChange}
                    disabled={!!invitedEmail}
                  />
                  {invitedEmail && (
                    <p className="text-xs text-muted-foreground mt-1">
                      This email is pre-filled from your invitation and cannot be changed.
                    </p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <Input
                    id="password"
                    type="password"
                    value={formData.password}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="name">Full Name</Label>
                  <Input
                    id="name"
                    placeholder="John Doe"
                    value={formData.name}
                    onChange={handleInputChange}
                  />
                </div>
              </div>
            )}

            {step === 2 && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Travel Preferences</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {["Beach", "Mountains", "City", "Countryside", "Adventure", "Relaxation"].map(
                      (pref) => (
                        <Button
                          key={pref}
                          variant={selectedTravelPreferences.includes(pref) ? "default" : "outline"}
                          className="justify-start"
                          onClick={() =>
                            togglePreference(
                              pref,
                              setSelectedTravelPreferences,
                              selectedTravelPreferences
                            )
                          }
                        >
                          {pref}
                        </Button>
                      )
                    )}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="budget">Typical Budget</Label>
                  <select
                    id="budget"
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    value={budget}
                    onChange={(e) => setBudget(e.target.value)}
                  >
                    <option value="budget-friendly">Budget-friendly</option>
                    <option value="mid-range">Mid-range</option>
                    <option value="luxury">Luxury</option>
                  </select>
                </div>
              </div>
            )}

            {step === 3 && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Availability</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {["Weekends", "Week-long", "2+ weeks", "Flexible"].map((avail) => (
                      <Button
                        key={avail}
                        variant={selectedAvailability.includes(avail) ? "default" : "outline"}
                        className="justify-start"
                        onClick={() =>
                          togglePreference(avail, setSelectedAvailability, selectedAvailability)
                        }
                      >
                        {avail}
                      </Button>
                    ))}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Preferred Travel Seasons</Label>
                  <MonthSelector
                    selectedMonths={selectedMonths}
                    onChange={setSelectedMonths}
                    onConfirm={() => {
                      // Optional: Add any additional logic when user confirms selection
                    }}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Travel Group</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {["Solo", "Couples", "Friends", "Family w/ Kids"].map((group) => (
                      <Button
                        key={group}
                        variant={selectedTravelGroups.includes(group) ? "default" : "outline"}
                        className="justify-start"
                        onClick={() =>
                          togglePreference(group, setSelectedTravelGroups, selectedTravelGroups)
                        }
                      >
                        {group}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {step === 4 && (
              <div className="space-y-4">
                {/* TODO: Re-enable photo upload functionality once file upload domain is implemented
                <div className="space-y-2">
                  <Label>Profile Picture</Label>
                  <div className="flex items-center justify-center p-6 border-2 border-dashed rounded-md">
                    <div className="text-center">
                      <div className="mt-2">
                        <Button variant="outline">Upload Photo</Button>
                      </div>
                      <p className="mt-1 text-xs text-muted-foreground">PNG, JPG up to 5MB</p>
                    </div>
                  </div>
                </div>
                */}
                <div className="space-y-2">
                  <Label>Location (Optional)</Label>
                  <DestinationAutocomplete
                    value={location}
                    onChange={(value, placeId) => {
                      setLocation(value)
                      setLocationPlaceId(placeId)
                    }}
                    placeholder="Where are you based?"
                    required={false}
                  />
                  <p className="text-xs text-muted-foreground">
                    Help us provide better travel suggestions based on your location
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="bio">Bio (Optional)</Label>
                  <textarea
                    id="bio"
                    rows={3}
                    placeholder="Tell your friends a bit about yourself..."
                    className="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    value={formData.bio}
                    onChange={handleInputChange}
                  ></textarea>
                </div>
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" onClick={prevStep} disabled={step === 1}>
              <ArrowLeft className="mr-2 h-4 w-4" /> Back
            </Button>
            {step < totalSteps ? (
              <Button onClick={nextStep}>
                Next <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            ) : (
              <Button onClick={handleSignup} disabled={loading}>
                {loading ? "Creating Account..." : "Complete Setup"}
              </Button>
            )}
          </CardFooter>
          <div className="text-center text-sm pb-4">
            Already have an account?{" "}
            <Link href="/login" className="text-primary hover:underline">
              Log In
            </Link>
          </div>
        </Card>
      </main>
    </div>
  )
}

function getStepDescription(step: number) {
  switch (step) {
    case 1:
      return "Account Information"
    case 2:
      return "Travel Preferences"
    case 3:
      return "Availability & Group Type"
    case 4:
      return "Profile Setup"
    default:
      return ""
  }
}
