import { NextRequest, NextResponse } from "next/server"

export async function GET(request: NextRequest) {
  try {
    // Rate limiting check - simple implementation based on IP
    const clientIP = request.ip || request.headers.get("x-forwarded-for") || "unknown"
    
    // Log the public places API request for monitoring
    console.log(`Public Places Autocomplete API request from IP: ${clientIP}`)
    
    const searchParams = request.nextUrl.searchParams
    const query = searchParams.get("query")

    if (!query) {
      return NextResponse.json({ error: "Query parameter is required" }, { status: 400 })
    }

    // Basic validation - limit query length to prevent abuse
    if (query.length > 100) {
      return NextResponse.json({ error: "Query too long" }, { status: 400 })
    }

    const apiKey = process.env.GOOGLE_PLACES_API_KEY

    if (!apiKey) {
      console.error("Google Places API key is not configured")
      return NextResponse.json({ error: "Places service is not configured" }, { status: 500 })
    }

    // Use Text Search API instead of Autocomplete API
    // This works better with referer restrictions
    // Add 'travel destination' to the query to get better results for travel locations
    const url = `https://maps.googleapis.com/maps/api/place/textsearch/json?query=${encodeURIComponent(query + " travel destination")}&key=${apiKey}`

    const response = await fetch(url)

    if (!response.ok) {
      const errorData = await response.json()
      console.error("Google Places API error:", errorData)
      return NextResponse.json(
        { error: "Failed to fetch place predictions" },
        { status: response.status }
      )
    }

    const data = await response.json()

    // Log the response for debugging
    console.log(`Public Places API returned ${data.results?.length || 0} results for query: ${query}`)

    // Format the results to match the autocomplete API structure
    const predictions =
      data.results?.map((place: { place_id: string; name: string; formatted_address?: string }) => {
        // Check if formatted_address already contains the place name to avoid duplication
        let description = place.name

        if (place.formatted_address) {
          const formattedLower = place.formatted_address.toLowerCase()
          const nameLower = place.name.toLowerCase()

          // Check if formatted_address already contains the place name (case insensitive)
          if (formattedLower.includes(nameLower)) {
            // Just use the formatted address as it already contains the name
            description = place.formatted_address
          } else {
            // Otherwise, combine them
            description = `${place.name}, ${place.formatted_address}`
          }
        }

        return {
          place_id: place.place_id,
          description: description,
        }
      }) || []

    // Limit results to prevent abuse
    const limitedPredictions = predictions.slice(0, 10)

    // Return the predictions
    return NextResponse.json({
      predictions: limitedPredictions,
    })
  } catch (error) {
    console.error("Error in Public Google Places Autocomplete API:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
