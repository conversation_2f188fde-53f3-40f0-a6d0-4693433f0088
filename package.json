{"name": "to<PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "type": "module", "engines": {"node": ">=18.17.1", "npm": ">=9.6.7"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\"", "check-types": "tsc --noEmit", "lint-staged": "lint-staged", "pre-commit": "lint-staged", "prepare": "husky"}, "dependencies": {"@getbrevo/brevo": "^2.2.0", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "latest", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.6", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "latest", "embla-carousel-react": "8.5.1", "firebase": "latest", "firebase-admin": "^13.2.0", "immer": "latest", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "next": "15.2.4", "next-themes": "latest", "openai": "latest", "react": "^19", "react-day-picker": "8.10.1", "react-dom": "^19", "react-firebase-hooks": "latest", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "sonner": "^1.7.1", "stripe": "^18.0.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "use-sync-external-store": "latest", "vaul": "^0.9.6", "workbox-core": "latest", "workbox-expiration": "latest", "workbox-precaching": "latest", "workbox-routing": "latest", "workbox-strategies": "latest", "ws": "latest", "zod": "^3.24.1", "zustand": "latest"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@eslint/js": "^9.24.0", "@next/eslint-plugin-next": "^15.3.0", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.29.1", "@typescript-eslint/parser": "^8.29.1", "dotenv": "^16.5.0", "eslint": "^9.24.0", "eslint-config-next": "^15.3.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-next": "^0.0.0", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react-hooks": "^5.2.0", "husky": "^9.1.7", "lint-staged": "^15.5.1", "postcss": "^8", "prettier": "^3.5.3", "tailwindcss": "^3.4.17", "typescript": "^5"}}