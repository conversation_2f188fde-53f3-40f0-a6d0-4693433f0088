"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>rk<PERSON> } from "lucide-react"
import { cn } from "@/lib/utils"

interface FloatingAIButtonProps {
  onClick: () => void
  className?: string
  children?: React.ReactNode
}

export function FloatingAIButton({ onClick, className, children }: FloatingAIButtonProps) {
  return (
    <Button
      onClick={onClick}
      className={cn(
        // Base positioning - use high z-index to stay above most elements
        "fixed bottom-6 right-6 z-[60]",
        // Size and shape
        "h-14 w-14 rounded-full p-0",
        // Colors with gradient effect
        "bg-gradient-to-br from-[#00796B] to-[#004D40]",
        "hover:from-[#00695C] hover:to-[#00332A]",
        "border-2 border-[#FFD54F]/20",
        // Shadow and glow effects
        "shadow-lg shadow-[#00796B]/25",
        "hover:shadow-xl hover:shadow-[#00796B]/40",
        // Shine effect
        "relative overflow-hidden",
        "before:absolute before:inset-0",
        "before:bg-gradient-to-br before:from-white/20 before:via-transparent before:to-transparent",
        "before:opacity-0 hover:before:opacity-100",
        "before:transition-opacity before:duration-300",
        // Touch-friendly sizing for mobile
        "min-h-[44px] min-w-[44px]",
        // Smooth transitions
        "transition-all duration-300 ease-in-out",
        "hover:scale-110 active:scale-95",
        // Focus styles
        "focus:outline-none focus:ring-2 focus:ring-[#FFD54F] focus:ring-offset-2",
        className
      )}
      aria-label="AI Suggestions"
    >
      {children || <Sparkles className="h-6 w-6 text-[#F5F5F5] drop-shadow-sm" />}
    </Button>
  )
}
